import { useEffect, useRef, useState } from 'react';

export const MarqueeText = ({
    children,
    duration = 2000,
    delay = 1000,
}: {
    children: React.ReactNode;
    duration?: number;
    delay?: number;
}   ) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const textRef = useRef<HTMLDivElement>(null);
    const [shouldAnimate, setShouldAnimate] = useState(false);
    const [animationId] = useState(() => `marquee-${Math.random().toString(36).substr(2, 9)}`);
  
    useEffect(() => {
      const checkIfAnimationNeeded = () => {
        if (containerRef.current && textRef.current) {
          const containerWidth = containerRef.current.offsetWidth;
          const textWidth = textRef.current.scrollWidth;
          
          if (textWidth > containerWidth) {
            const distance = textWidth - containerWidth;
            setShouldAnimate(true);
            
            // Create dynamic keyframes
            const totalDuration = duration * 2 + delay * 2;
            const phase1 = (duration / totalDuration) * 100;
            const phase2 = ((duration + delay) / totalDuration) * 100;
            const phase3 = ((duration * 2 + delay) / totalDuration) * 100;
            
            const keyframes = `
              @keyframes ${animationId} {
                0% { transform: translateX(0px); }
                ${phase1}% { transform: translateX(-${distance}px); }
                ${phase2}% { transform: translateX(-${distance}px); }
                ${phase3}% { transform: translateX(0px); }
                100% { transform: translateX(0px); }
              }
            `;
            
            // Remove existing style if any
            const existingStyle = document.getElementById(animationId);
            if (existingStyle) {
              existingStyle.remove();
            }
            
            // Add new style
            const style = document.createElement('style');
            style.id = animationId;
            style.textContent = keyframes;
            document.head.appendChild(style);
            
          } else {
            setShouldAnimate(false);
          }
        }
      };
  
      const timer = setTimeout(checkIfAnimationNeeded, 100);
      
      window.addEventListener('resize', checkIfAnimationNeeded);
      return () => {
        clearTimeout(timer);
        window.removeEventListener('resize', checkIfAnimationNeeded);
        // Cleanup style on unmount
        const style = document.getElementById(animationId);
        if (style) {
          style.remove();
        }
      };
    }, [children, duration, delay, animationId]);
  
    const totalDuration = duration * 2 + delay * 2;
  
    return (
      <div className="marquee-container" ref={containerRef}>
        <div 
          className="marquee-text"
          ref={textRef}
          style={{
            animation: shouldAnimate ? `${animationId} ${totalDuration}ms infinite linear` : 'none'
          }}
        >
          {children}
        </div>
        
        <style>{`
          .marquee-container {
            overflow: hidden;
            line-height: initial;
            text-align: start;
            font-size: initial;
          }
          .marquee-text {
            white-space: nowrap;
            display: inline-block;
            will-change: transform;
          }
        `}</style>
      </div>
    );
  };
  