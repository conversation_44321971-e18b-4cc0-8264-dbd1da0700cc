import { vi, describe, it, expect, beforeEach } from 'vitest';
import * as device from '@/lib/zlpSdk/device';

vi.mock('@/lib/zlpSdk/device', () => ({
  getDeviceIdZPA: vi.fn(),
  getAppInfo: vi.fn(),
}));

vi.mock('../version', () => ({
  getMiniAppVersion: () => '1.2.3',
}));

import { finSuggestSOFButtonHeaderInterceptor } from '../finSuggestSOFButtonHeaderInterceptor';

describe('finSuggestSOFButtonHeaderInterceptor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should add correct headers for ZPA', async () => {
    // @ts-ignore
    global.navigator = { userAgent: 'ZaloPayClient' };
    (device.getDeviceIdZPA as any).mockResolvedValue('device-123');
    (device.getAppInfo as any).mockResolvedValue({
      userAgent: 'ua',
      os: 'ios',
      appVersion: '2.0.0',
    });
    const request = { headers: { foo: 'bar' } };
    const result = await finSuggestSOFButtonHeaderInterceptor(request);
    expect(result.headers).toMatchObject({
      foo: 'bar',
      'x-mini-app-version': '1.2.3',
      'x-platform': 'ZPA',
      'x-device-id': 'device-123',
      'x-user-agent': 'ua',
      'x-device-os': 'ios',
      'x-app-version': '2.0.0',
    });
  });

  it('should add correct headers for ZMP', async () => {
    // @ts-ignore
    global.navigator = { userAgent: 'OtherClient' };
    (device.getDeviceIdZPA as any).mockResolvedValue('device-456');
    (device.getAppInfo as any).mockResolvedValue({
      userAgent: 'ua2',
      os: 'android',
      appVersion: '3.0.0',
    });
    const request = { headers: { baz: 'qux' } };
    const result = await finSuggestSOFButtonHeaderInterceptor(request);
    expect(result.headers).toMatchObject({
      baz: 'qux',
      'x-mini-app-version': '1.2.3',
      'x-platform': 'ZMP',
      'x-device-id': 'device-456',
      'x-user-agent': 'ua2',
      'x-device-os': 'android',
      'x-app-version': '3.0.0',
    });
  });
});
