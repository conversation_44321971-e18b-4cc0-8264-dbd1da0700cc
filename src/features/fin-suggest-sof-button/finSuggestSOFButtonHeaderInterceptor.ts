// /* eslint-disable no-undef */
import { getAppInfo, getDeviceIdZPA } from '@/lib/zlpSdk/device';
import { getMiniAppVersion } from './version';

export const isZPA = () => navigator.userAgent?.indexOf('ZaloPayClient') > -1;

export const finSuggestSOFButtonHeaderInterceptor = async (request: RequestInit) => {
  const [deviceId, appInfo] = await Promise.all([getDeviceIdZPA(), getAppInfo()]);

  return {
    ...request,
    headers: {
      ...request.headers,
      'x-mini-app-version': getMiniAppVersion(),
      'x-platform': isZPA() ? 'ZPA' : 'ZMP',
      'x-device-id': deviceId,
      'x-user-agent': appInfo.userAgent,
      'x-device-os': appInfo.os,
      'x-app-version': appInfo.appVersion,
    },
  };
};
