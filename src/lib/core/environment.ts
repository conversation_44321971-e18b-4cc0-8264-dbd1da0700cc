export let moduleId: number | undefined;
export const setModuleId = (value: number | undefined) => {
  moduleId = value;
};

export enum Environment {
  MC_SANDBOX = 1,
  STAGING = 2,
  PRODUCTION = 3,
  DEV_SANDBOX = 4,
  QC_SANDBOX = 5,
}
export let environment: Environment;
export const setEnvironment = (value: Environment) => {
  environment = value;
};

const isRunLocal = process.env.REACT_APP_ENV === "local";
export const appOrigin = window.location.origin;
export const appBase = isRunLocal ? "/test-app" : "/installment";
export const baseName = window.__BASE_NAME__ || "/spa/v2/";
export const rootBase = `${baseName}${appBase}`;

// Get the current path relative to the rootBase
export const getCurrentPath = () => {
  const fullPath = window.location.pathname;
  const relativePath = fullPath.startsWith(rootBase) ? fullPath.substring(rootBase.length) : fullPath;
  const currentPath = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
  return currentPath;
};
