import { ReactNode } from "react";
import { cn } from "@/lib/utils";

export function Marquee({
    children,
    direction = "left",
    pauseOnHover = false,
    reverse = false,
    fade = false,
    bounce = false,
    className,
    innerClassName,
    numberOfCopies = 2,
  }: {
    children: ReactNode;
    direction?: "left" | "up";
    pauseOnHover?: boolean;
    reverse?: boolean;
    fade?: boolean;
    bounce?: boolean;
    className?: string;
    innerClassName?: string;
    numberOfCopies?: number;
  }) {
    return (
      <div
        className={cn(
          "group flex overflow-hidden",
          bounce ? "gap-0" : "gap-[1rem]",
          direction === "left" ? "flex-row" : "flex-col",
          className
        )}
        style={{
          maskImage: fade
            ? `linear-gradient(${
                direction === "left" ? "to right" : "to bottom"
              }, transparent 0%, rgba(0, 0, 0, 1.0) 10%, rgba(0, 0, 0, 1.0) 90%, transparent 100%)`
            : undefined,
          WebkitMaskImage: fade
            ? `linear-gradient(${
                direction === "left" ? "to right" : "to bottom"
              }, transparent 0%, rgba(0, 0, 0, 1.0) 10%, rgba(0, 0, 0, 1.0) 90%, transparent 100%)`
            : undefined,
        }}
      >
        {Array(bounce ? 1 : numberOfCopies)
          .fill(0)
          .map((_, i) => (
            <div
              key={i}
              className={cn(
                "flex shrink-0",
                bounce ? "gap-0 min-w-full" : "justify-around gap-[1rem] [--gap:1rem]",
                direction === "left"
                  ? bounce
                    ? "animate-marquee-bounce-left flex-row whitespace-nowrap"
                    : "animate-marquee-left flex-row"
                  : bounce
                  ? "animate-marquee-bounce-up flex-col"
                  : "animate-marquee-up flex-col",
                pauseOnHover && "group-hover:[animation-play-state:paused]",
                reverse && "direction-reverse",
                innerClassName
              )}
            >
              {children}
            </div>
          ))}
      </div>
    );
  }