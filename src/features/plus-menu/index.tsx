import {
  GradientOverlay,
  StyleSheetMotion,
} from "@/components/ui/warp-overlay";
import { cn } from "@/lib/utils";
import { AnimatePresence } from "motion/react";
import React, { useState, useEffect, useRef } from "react";

// Animation States
type AnimationState = "idle" | "opening" | "open" | "closing";

// Animation Settings Interface
interface AnimationSettings {
  buttonRotationDuration: number;
  colorTransitionDuration: number;
  backdropFadeDuration: number;
  optionStaggerDelay: number;
  optionAnimationDuration: number;
  optionTranslateDistance: number;
  closeDelay: number;
  buttonHoverScale: number;
  buttonActiveScale: number;
  buttonEasing: string;
  optionEasing: string;
  backdropEasing: string;
}

const EASING_OPTIONS = {
  ease: "ease",
  "ease-in": "ease-in",
  "ease-out": "ease-out",
  "ease-in-out": "ease-in-out",
  linear: "linear",
  bounce: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
  elastic: "cubic-bezier(0.175, 0.885, 0.32, 1.275)",
  back: "cubic-bezier(0.68, -0.6, 0.32, 1.6)",
  spring: "cubic-bezier(0.34, 1.56, 0.64, 1)",
  smooth: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  sharp: "cubic-bezier(0.4, 0, 0.6, 1)",
  overshoot: "cubic-bezier(0.25, 0.46, 0.45, 1.94)",
};

// Settings Panel Components
interface EasingSelectorProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
}

const EasingSelector: React.FC<EasingSelectorProps> = ({
  label,
  value,
  onChange,
}) => {
  const id = `easing-${label.toLowerCase().replace(/\s+/g, "-")}`;

  return (
    <div className="mb-3">
      <label
        htmlFor={id}
        className="block mb-1 text-xs font-medium text-gray-700"
      >
        {label}
      </label>
      <select
        id={id}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
      >
        {Object.entries(EASING_OPTIONS).map(([key, easingValue]) => (
          <option key={key} value={easingValue}>
            {key}
          </option>
        ))}
      </select>
    </div>
  );
};

interface NumberInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

const NumberInput: React.FC<NumberInputProps> = ({
  label,
  value,
  onChange,
  min = 0,
  max = 2000,
  step = 1,
  unit = "ms",
}) => {
  const id = `number-${label.toLowerCase().replace(/\s+/g, "-")}`;

  return (
    <div className="mb-3">
      <label
        htmlFor={id}
        className="block mb-1 text-xs font-medium text-gray-700"
      >
        {label} {unit && `(${unit})`}
      </label>
      <input
        id={id}
        type="number"
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        min={min}
        max={max}
        step={step}
        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
      />
    </div>
  );
};

interface SettingsPanelProps {
  settings: AnimationSettings;
  onSettingsChange: (settings: AnimationSettings) => void;
  isVisible: boolean;
  onClose: () => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  onSettingsChange,
  isVisible,
  onClose,
}) => {
  const updateSetting = <K extends keyof AnimationSettings>(
    key: K,
    value: AnimationSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value,
    });
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 left-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-30 max-h-[80vh] overflow-y-auto">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-gray-800">
          Animation Settings
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="text-lg leading-none text-gray-500 hover:text-gray-700"
          aria-label="Close settings"
        >
          ×
        </button>
      </div>

      <div className="w-64">
        {/* Easing Options */}
        <div className="mb-4">
          <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">
            Easing
          </h4>
          <EasingSelector
            label="Button Easing"
            value={settings.buttonEasing}
            onChange={(value) => updateSetting("buttonEasing", value)}
          />
          <EasingSelector
            label="Option Easing"
            value={settings.optionEasing}
            onChange={(value) => updateSetting("optionEasing", value)}
          />
          <EasingSelector
            label="Backdrop Easing"
            value={settings.backdropEasing}
            onChange={(value) => updateSetting("backdropEasing", value)}
          />
        </div>

        {/* Duration Settings */}
        <div className="mb-4">
          <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">
            Durations
          </h4>
          <NumberInput
            label="Button Rotation"
            value={settings.buttonRotationDuration}
            onChange={(value) => updateSetting("buttonRotationDuration", value)}
            max={1000}
          />
          <NumberInput
            label="Color Transition"
            value={settings.colorTransitionDuration}
            onChange={(value) =>
              updateSetting("colorTransitionDuration", value)
            }
            max={1000}
          />
          <NumberInput
            label="Backdrop Fade"
            value={settings.backdropFadeDuration}
            onChange={(value) => updateSetting("backdropFadeDuration", value)}
            max={1000}
          />
          <NumberInput
            label="Option Animation"
            value={settings.optionAnimationDuration}
            onChange={(value) =>
              updateSetting("optionAnimationDuration", value)
            }
            max={1000}
          />
        </div>

        {/* Timing Settings */}
        <div className="mb-4">
          <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">
            Timing
          </h4>
          <NumberInput
            label="Option Stagger Delay"
            value={settings.optionStaggerDelay}
            onChange={(value) => updateSetting("optionStaggerDelay", value)}
            max={200}
          />
          <NumberInput
            label="Close Delay"
            value={settings.closeDelay}
            onChange={(value) => updateSetting("closeDelay", value)}
            max={2000}
          />
        </div>

        {/* Scale Settings */}
        <div className="mb-4">
          <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">
            Scale
          </h4>
          <NumberInput
            label="Button Hover Scale"
            value={settings.buttonHoverScale}
            onChange={(value) => updateSetting("buttonHoverScale", value)}
            min={0.5}
            max={2}
            step={0.05}
            unit=""
          />
          <NumberInput
            label="Button Active Scale"
            value={settings.buttonActiveScale}
            onChange={(value) => updateSetting("buttonActiveScale", value)}
            min={0.5}
            max={2}
            step={0.05}
            unit=""
          />
        </div>

        {/* Distance Settings */}
        <div>
          <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">
            Distance
          </h4>
          <NumberInput
            label="Option Translate Distance"
            value={settings.optionTranslateDistance}
            onChange={(value) =>
              updateSetting("optionTranslateDistance", value)
            }
            max={200}
            unit="px"
          />
        </div>
      </div>
    </div>
  );
};

// Option Button
interface OptionButtonProps {
  iconName: string;
  title: string;
  description: string;
  animationState: AnimationState;
  delay: number;
  settings: AnimationSettings;
  onClick?: () => void;
}

const OptionButton: React.FC<OptionButtonProps> = ({
  iconName,
  title,
  description,
  animationState,
  delay,
  settings,
  onClick,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (animationState === "opening") {
      // Mount immediately but keep invisible
      setIsMounted(true);
      // Delay visibility for stagger effect
      timeoutRef.current = setTimeout(() => {
        setIsVisible(true);
      }, delay * settings.optionStaggerDelay);
    } else if (animationState === "open") {
      setIsMounted(true);
      setIsVisible(true);
    } else if (animationState === "closing") {
      // Start hiding immediately
      setIsVisible(false);
      // Unmount after animation completes
      timeoutRef.current = setTimeout(() => {
        setIsMounted(false);
      }, settings.optionAnimationDuration);
    } else {
      setIsVisible(false);
      setIsMounted(false);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [
    animationState,
    delay,
    settings.optionStaggerDelay,
    settings.optionAnimationDuration,
  ]);

  const getIcon = (name: string) => {
    const iconMap: { [key: string]: string } = {
      "camera-outline": "📷",
      "qr-code-outline": "📱",
      "mic-outline": "🎤",
      "create-outline": "✏️",
    };
    return iconMap[name] || "•";
  };

  if (!isMounted) return null;

  return (
    <div
      className="flex items-center mb-2"
      style={{
        transform: isVisible
          ? "translateY(0)"
          : `translateY(${settings.optionTranslateDistance}px)`,
        opacity: isVisible ? 1 : 0,
        filter: isVisible ? "blur(0)" : "blur(4px)",
        transition: `all ${settings.optionAnimationDuration}ms ${settings.optionEasing}`,
        willChange: "transform, opacity, filter",
      }}
    >
      <div
        className="flex items-center px-4 py-3 transition-shadow duration-200 bg-white shadow-lg cursor-pointer rounded-xl hover:shadow-xl"
        style={{ minWidth: "200px" }}
        onClick={onClick}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onClick?.();
          }
        }}
        role="button"
        tabIndex={0}
      >
        <div className="mr-3 text-2xl">{getIcon(iconName)}</div>
        <div className="flex-1 text-left">
          <div className="text-sm font-semibold text-gray-800">{title}</div>
          <div className="text-xs text-gray-500">{description}</div>
        </div>
      </div>
    </div>
  );
};

// Main PlusMenu Component
const PlusMenu: React.FC = () => {
  const [animationState, setAnimationState] = useState<AnimationState>("idle");
  const [isHovered, setIsHovered] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [isSettingsPanelVisible, setIsSettingsPanelVisible] = useState(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const [settings, setSettings] = useState<AnimationSettings>({
    buttonRotationDuration: 300,
    colorTransitionDuration: 300,
    backdropFadeDuration: 200,
    optionStaggerDelay: 60,
    optionAnimationDuration: 400,
    optionTranslateDistance: 100,
    closeDelay: 150,
    buttonHoverScale: 1.05,
    buttonActiveScale: 0.95,
    buttonEasing: "ease-in-out",
    optionEasing: "cubic-bezier(0.34, 1.56, 0.64, 1)",
    backdropEasing: "ease-out",
  });

  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  const openMenu = () => {
    setAnimationState("opening");
    // Transition to fully open after all animations complete
    const totalOpenTime =
      4 * settings.optionStaggerDelay + settings.optionAnimationDuration;
    animationTimeoutRef.current = setTimeout(() => {
      setAnimationState("open");
    }, totalOpenTime);
  };

  const closeMenu = () => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    setAnimationState("closing");
    animationTimeoutRef.current = setTimeout(() => {
      setAnimationState("idle");
    }, settings.closeDelay);
  };

  const toggle = () => {
    if (animationState === "idle") {
      openMenu();
    } else if (animationState === "open" || animationState === "opening") {
      closeMenu();
    }
    // Ignore clicks during closing animation
  };

  const handleOptionClick = (option: string) => {
    console.log(`Selected: ${option}`);
    closeMenu();
  };

  const toggleSettingsPanel = () => {
    setIsSettingsPanelVisible(!isSettingsPanelVisible);
  };

  const currentScale = isActive
    ? settings.buttonActiveScale
    : isHovered
    ? settings.buttonHoverScale
    : 1;

  const isMenuVisible = animationState !== "idle";
  const isBackdropVisible =
    animationState === "opening" || animationState === "open";

  const size = { width: window.innerWidth, height: window.innerHeight };
  const listOption = [
    {
      iconName: "create-outline",
      title: "Hóa đơn",
      description: "Kiểm tra hóa đơn gần đây",
      delay: 4,
    },
    {
      iconName: "mic-outline",
      title: "Tài chính",
      description: "Danh mục sở hữu",
      delay: 3,
    },
    {
      iconName: "qr-code-outline",
      title: "Scan QR",
      description: "Scan QR code",
      delay: 2,
    },
    {
      iconName: "camera-outline",
      title: "Chia tiền",
      description: "Quét bill chia tiền",
      delay: 1,
    },
  ];
  return (
    <>
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
      <div className={cn("overlay-root", isSettingsPanelVisible ? "" : "pointer-events-none")} onClick={close}>
        {isMenuVisible && (
          <AnimatePresence>
            {isBackdropVisible ? <GradientOverlay size={size} /> : null}
          </AnimatePresence>
        )}
        <div className="fixed inset-0 z-[100000]">
          {/* Settings Panel */}
          <SettingsPanel
            settings={settings}
            onSettingsChange={setSettings}
            isVisible={isSettingsPanelVisible}
            onClose={() => setIsSettingsPanelVisible(false)}
          />

          {/* Settings Toggle Button */}
          <button
            type="button"
            onClick={toggleSettingsPanel}
            className="fixed z-30 p-2 transition-shadow bg-white border border-gray-200 rounded-lg shadow-lg pointer-events-auto top-3 right-3 hover:shadow-xl"
            aria-label="Toggle animation settings"
          >
            <div className="text-gray-600">⚙️</div>
          </button>

          {/* Backdrop Blur */}
          {/* {isMenuVisible && 
        (<div
          className="absolute inset-0 pointer-events-auto"
          style={{
            zIndex: 20,
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            backdropFilter: isBackdropVisible ? 'blur(8px)' : 'blur(0px)',
            opacity: isBackdropVisible ? 1 : 0,
            transition: `opacity ${settings.backdropFadeDuration}ms ${EASING_OPTIONS.spring}`,
            willChange: 'opacity'
          }}
          onClick={toggle}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggle();
            }
          }}
          tabIndex={0}
          role="button"
          aria-label="Close menu"
        />
      )} */}

          {/* Floating Action Button & Options */}
          <div
            className="absolute flex flex-col items-center -translate-x-1/2 pointer-events-none left-1/2 bottom-24"
            // style={{ right: "20px", bottom: "40px", zIndex: 25 }}
          >
            {/* Action Buttons */}
            <div className="flex flex-col items-end mb-2 pointer-events-auto">
              {listOption.map((option) => (
                <OptionButton
                  key={option.title}
                  iconName={option.iconName}
                  title={option.title}
                  description={option.description}
                  animationState={animationState}
                  delay={option.delay}
                  settings={settings}
                  onClick={() => handleOptionClick(option.title)}
                />
              ))}
            </div>

            {/* Main FAB */}
            <div className="flex flex-row items-center justify-start">
               {/* {isMenuVisible ? (<div>
              <Textarea className="flex-1 w-full h-12" />
            </div>) : null} */}
            <div
              role="button"
              aria-label="Toggle action menu"
              tabIndex={0}
              className="flex items-center justify-center transition-all shadow-lg cursor-pointer pointer-events-auto rounded-2xl"
              style={{
                width: "60px",
                height: "60px",
                backgroundColor: isMenuVisible ? "#000000" : "#ffffff",
                boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
                transform: `scale(${currentScale})`,
                transition: `background-color ${settings.colorTransitionDuration}ms ${settings.buttonEasing}, transform 200ms ease-out`,
                willChange: "transform, background-color",
              }}
              onClick={toggle}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onMouseDown={() => setIsActive(true)}
              onMouseUp={() => setIsActive(false)}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  toggle();
                }
              }}
            > 
              <div
                className="relative"
                style={{
                  transform: `rotate(${isMenuVisible ? 45 : 0}deg)`,
                  transition: `transform ${settings.buttonRotationDuration}ms ${settings.buttonEasing}`,
                  willChange: "transform",
                }}
              >
                <div
                  className="absolute rounded-sm"
                  style={{
                    width: "18px",
                    height: "2px",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    backgroundColor: isMenuVisible ? "#ffffff" : "#000000",
                    transition: `background-color ${settings.colorTransitionDuration}ms ${settings.buttonEasing}`,
                  }}
                />
                <div
                  className="absolute rounded-sm"
                  style={{
                    width: "18px",
                    height: "2px",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%) rotate(90deg)",
                    backgroundColor: isMenuVisible ? "#ffffff" : "#000000",
                    transition: `background-color ${settings.colorTransitionDuration}ms ${settings.buttonEasing}`,
                  }}
                />
              </div>
            </div>
            </div>
          </div>
        </div>
        <StyleSheetMotion />
      </div>
    </>
  );
};

export default PlusMenu;
