import { describe, it, vi, beforeEach, expect } from "vitest";
import { postConsultPaymentOffer } from "../api";
import createZlpRequest from "@/lib/core/createZlpRequest";
import { HttpRequestVerbs } from "@/lib/core/types";
import { customAuthorizeInterceptor } from "@/lib/core/interceptors/customAuthorizeInterceptor";
import { finSuggestSOFButtonHeaderInterceptor } from "../finSuggestSOFButtonHeaderInterceptor";
import { withBaseUrl, PATH } from "../constants";

vi.mock("@/lib/core/createZlpRequest");

describe("postConsultPaymentOffer", () => {
  const payload = { orders: [{ app_id: 1, amount: 1000 }] };
  const mockBuild = vi.fn();
  const mockSetDataBody = vi.fn(() => ({ build: mockBuild }));
  const mockSetHeaders = vi.fn(() => ({ setDataBody: mockSetDataBody }));
  const mockAddRequestInterceptors = vi.fn(() => ({ setHeaders: mockSetHeaders }));

  beforeEach(() => {
    vi.clearAllMocks();
    (createZlpRequest as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      addRequestInterceptors: mockAddRequestInterceptors,
    } as any);
  });

  it("should call createZlpRequest with correct params and chain methods", async () => {
    await postConsultPaymentOffer(payload);
    expect(createZlpRequest).toHaveBeenCalledWith(
      withBaseUrl({ path: PATH }),
      HttpRequestVerbs.POST
    );
    expect(mockAddRequestInterceptors).toHaveBeenCalledWith([
      customAuthorizeInterceptor,
      finSuggestSOFButtonHeaderInterceptor,
    ]);
    expect(mockSetHeaders).toHaveBeenCalledWith({
      "Content-Type": "application/json",
    });
    expect(mockSetDataBody).toHaveBeenCalledWith(JSON.stringify(payload));
    expect(mockBuild).toHaveBeenCalled();
  });

  it("should throw if build throws", async () => {
    mockBuild.mockImplementationOnce(() => { throw new Error("fail"); });
    await expect(postConsultPaymentOffer(payload)).rejects.toThrow("fail");
  });

  it("should handle empty orders array", async () => {
    const emptyPayload = { orders: [] };
    await postConsultPaymentOffer(emptyPayload);
    expect(mockSetDataBody).toHaveBeenCalledWith(JSON.stringify(emptyPayload));
    expect(mockBuild).toHaveBeenCalled();
  });

  it("should pass through custom headers if setHeaders is changed", async () => {
    const customHeaders = { "X-Test": "test" };
    mockSetHeaders.mockClear();
    // Simulate a custom setHeaders call
    (createZlpRequest as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
      addRequestInterceptors: () => ({
        setHeaders: (headers: any) => {
          expect(headers["Content-Type"]).toBe("application/json");
          expect(headers["X-Test"]).toBeUndefined();
          return { setDataBody: mockSetDataBody };
        },
      }),
    } as any);
    await postConsultPaymentOffer(payload);
  });
});
