import "./styles/globals.css";
import React from "react";
import ReactDOM<PERSON>lient from "react-dom/client";
import App from "./App";
import singleSpaReact from "single-spa-react";

const reactLifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  domElementGetter: () => {
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    return document.getElementById("root")!;
  },
  errorBoundary(err, info, props) {
    return <div className="flex items-center justify-between h-16 px-6 text-white bg-primary">Ứng dụng chưa có sẵn</div>;
  },
});

export const bootstrap = reactLifecycles.bootstrap;

export const mount = reactLifecycles.mount;

export const unmount = reactLifecycles.unmount;
