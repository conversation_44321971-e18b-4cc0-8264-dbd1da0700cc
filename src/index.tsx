// import React from "react";
// import ReactDOM from "react-dom/client";
// import "./styles/globals.css";
// import { RouterProvider } from "react-router-dom";
// import router from "./routes";

// // This is a fallback for development without the React Router plugin
// // In production, the React Router plugin will handle this

// const rootEl = document.getElementById("root");
// if (rootEl) {
//   const root = ReactDOM.createRoot(rootEl);
//   root.render(
//     <React.StrictMode>
//       <RouterProvider router={router} />
//     </React.StrictMode>,
//   );
// }
import('./zpi-app').then(async module => await module.mount({}));
