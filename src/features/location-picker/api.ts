import createZlpRequest from "@/lib/core/createZlpRequest";
import { HttpRequestVerbs } from "@/lib/core/types";
import { withBaseLocationUrl } from "./constants";

export type CityModel = {
  id: number;
  name: string;
  city_name: string;
  latitude: number;
  longitude: number;
};

export const getUserLocation = async ({
  latitude = -1,
  longitude = -1,
}: {
  latitude?: number;
  longitude?: number;
}) => {
  const res = await createZlpRequest<{ location: CityModel }>(
    withBaseLocationUrl(),
    HttpRequestVerbs.GET
  )
    .setQueryParams(
      Number(latitude) > 0
        ? {
            latitude,
            longitude,
          }
        : {}
    )
    .build();

  return res;
};
