
@theme inline {
	--color-white: oklch(1 0 0);
	--color-background: oklch(0.9808 0.0091 258.34);
	--color-overlay: oklch(0.2373 0.0697 251.37 / 80%);
	--color-stroke-v2: oklch(0.9704 0.0045 214.33);

	/* Primary */
	--color-primary: oklch(0.4227 0.2318 263.7);
	--color-primary-blue: oklch(0.4227 0.2318 263.7);
	--color-primary-green: oklch(0.7483 0.197457 151.8608);
	--color-primary-dark: oklch(0.2373 0.0697 251.37);
	--color-primary-white: oklch(1 0 0);

	/* Secondary */
	--color-secondary-red: oklch(0.5852 0.2266 20.84);
	--color-secondary-orange: oklch(0.7521 0.1786 58.75);
	--color-secondary-teal: oklch(0.7369 0.1582 235.85);
	--color-secondary-indigo: oklch(0.4857 0.2644 293.04);

	/* Dark */
	--color-dark-25: oklch(0.9659 0.0025 228.78);
	--color-dark-50: oklch(0.9326 0.0052 247.88);
	--color-dark-100: oklch(0.8609 0.0106 247.95);
	--color-dark-200: oklch(0.7166 0.0233 250.29);
	--color-dark-300: oklch(0.5673 0.0358 247.03);
	--color-dark-400: oklch(0.4074 0.0523 249.22);
	--color-dark-500: oklch(0.2373 0.0697 251.37);
	--color-dark-600: oklch(0.21 0.0592 249.86);
	--color-dark-700: oklch(0.1811 0.0467 246.17);
	--color-dark-800: oklch(0.1487 0.0367 244.03);
	--color-dark-900: oklch(0.1151 0.0241 233.92);

	/* Red */
	--color-red-25: oklch(0.9733 0.0121 4.74);
	--color-red-50: oklch(0.9481 0.0224 3.82);
	--color-red-100: oklch(0.8966 0.0457 6.19);
	--color-red-200: oklch(0.7968 0.0988 7.58);
	--color-red-300: oklch(0.7066 0.1512 10.44);
	--color-red-400: oklch(0.6332 0.1996 14.59);
	--color-red-500: oklch(0.5852 0.2266 20.84);
	--color-red-600: oklch(0.4971 0.1912 20.39);
	--color-red-700: oklch(0.4034 0.1525 19.68);
	--color-red-800: oklch(0.306 0.1123 18.35);
	--color-red-900: oklch(0.199 0.0658 14.72);

	/* Orange */
	--color-orange-25: oklch(0.9871 0.0201 94.52);
	--color-orange-50: oklch(0.9713 0.0465 95.19);
	--color-orange-100: oklch(0.9383 0.0899 93.16);
	--color-orange-200: oklch(0.8967 0.1334 90.16);
	--color-orange-300: oklch(0.8361 0.16 80.16);
	--color-orange-400: oklch(0.7928 0.1699 70.34);
	--color-orange-500: oklch(0.7521 0.1786 58.75);
	--color-orange-600: oklch(0.6381 0.1503 59.55);
	--color-orange-700: oklch(0.5186 0.1206 60.86);
	--color-orange-800: oklch(0.3899 0.0895 62.25);
	--color-orange-900: oklch(0.2516 0.0552 68.01);

	/* Green */
	--color-green-25: oklch(0.9884 0.0164 162.77);
	--color-green-50: oklch(0.9847 0.0221 160.25);
	--color-green-100: oklch(0.9664 0.049 163.74);
	--color-green-200: oklch(0.9394 0.0927 161.89);
	--color-green-300: oklch(0.9058 0.1544 159.37);
	--color-green-400: oklch(0.8574 0.1981 155.54);
	--color-green-500: oklch(0.7483 0.197457 151.8608);
	--color-green-600: oklch(0.6352 0.1664 152.19);
	--color-green-700: oklch(0.514 0.1325 152.92);
	--color-green-800: oklch(0.3879 0.09876 153.5375);
	--color-green-900: oklch(0.2472 0.058953 156.9132);

	/* Blue */
	--color-blue-25: oklch(0.9647 0.019972 233.8456);
	--color-blue-50: oklch(0.9347 0.0366 235.46);
	--color-blue-100: oklch(0.8772 0.0666 241.11);
	--color-blue-200: oklch(0.7982 0.1084 246.25);
	--color-blue-300: oklch(0.6883 0.166 256.22);
	--color-blue-400: oklch(0.5901 0.2235 261.87);
	--color-blue-500: oklch(0.4227 0.2318 263.7);
	--color-blue-600: oklch(0.3619 0.1945 263.56);
	--color-blue-700: oklch(0.2985 0.1551 263.29);
	--color-blue-800: oklch(0.2287 0.1127 262.77);
	--color-blue-900: oklch(0.1939 0.09 262.05);

	/* Teal */
	--color-teal-25: oklch(0.9805 0.0108 234.81);
	--color-teal-50: oklch(0.9764 0.0133 233.74);
	--color-teal-100: oklch(0.9523 0.0275 232.66);
	--color-teal-200: oklch(0.9059 0.0603 226.17);
	--color-teal-300: oklch(0.8408 0.1076 224.29);
	--color-teal-400: oklch(0.7799 0.1433 228.15);
	--color-teal-500: oklch(0.7369 0.1582 235.85);
	--color-teal-600: oklch(0.6185 0.1449 241.29);
	--color-teal-700: oklch(0.5253 0.124077 241.7448);
	--color-teal-800: oklch(0.4626 0.1054 239.68);
	--color-teal-900: oklch(0.407 0.0888 238.99);

	/* Indigo */
	--color-indigo-25: oklch(0.9653 0.019 299.07);
	--color-indigo-50: oklch(0.9379 0.034259 298.5262);
	--color-indigo-100: oklch(0.8824 0.0652 296.74);
	--color-indigo-200: oklch(0.7935 0.1195 297.29);
	--color-indigo-300: oklch(0.6868 0.1894 297.24);
	--color-indigo-400: oklch(0.5887 0.2544 295.42);
	--color-indigo-500: oklch(0.4857 0.2644 293.04);
	--color-indigo-600: oklch(0.414 0.223 293.59);
	--color-indigo-700: oklch(0.3388 0.18 294.51);
	--color-indigo-800: oklch(0.2573 0.1324 295.72);
	--color-indigo-900: oklch(0.17 0.0829 299.83);
}
