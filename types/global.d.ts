declare global {
  var __DEV__: boolean;
  
  interface Window {
    __BASE_NAME__?: string;
    __APP_INFO__?: {
      env?: string;
    };
    __ZPI_ZMP_SDK__?: {
      getZlpToken(): { zlp_token?: string };
    };
    zlpSdk: {
      Tracking: {
        trackEvent(options?: any): Promise<ITrackingResult>
      };
      Location: {
        getLocation(options?: any): Promise<any>;
        checkLocationPermission(): Promise<any>;
        requestLocationPermission(): Promise<any>;
      };
      Device: {
        getDeviceId(): Promise<any>;
        appInfo(): Promise<any>;
        openSystemSettings(): Promise<any>;
      };
    };
  }
}

export {};
