#!/usr/bin/env bash

HOME=$(pwd)

# Extract version value from package.json
date=$(date '+%d%m%Y.%H%M')
npm_package_version="$(node -p "require('./package.json').version")-${date}"

if [ ! -z $2 ]; then
  npm_package_version=$2
fi

# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi

# Load .env.local file
if [ -e "${HOME}/.env.local" ]; then
  . "${HOME}/.env.local"
fi

if [ ! -z $1 ]; then
  if [ $1 == "qc" ]; then
    # Load .env.develop file
    . "${HOME}/.env.develop"
  elif [ $1 == "stg" ]; then
    # Load .env.staging file
    . "${HOME}/.env.staging"
  elif [ $1 == "cdn" ]; then
      # Load .env.staging file
      . "${HOME}/.env.cdn"
  elif [  $1 == "prod" ]; then
    # Load .env.product file
    . "${HOME}/.env.product"
  fi
fi
echo "env with arg: $1"
echo "NODE_ENV: ${NODE_ENV}"
echo "npm_package_version: ${npm_package_version}"
echo "PUBLIC_URL: ${PUBLIC_URL}"
echo "GENERATE_SOURCEMAP: ${GENERATE_SOURCEMAP}"
echo "APP_NAME: ${APP_NAME}"
echo "APP_PATH: ${APP_PATH}"
echo "REACT_APP_BASE_API_URL: ${REACT_APP_BASE_API_URL}"
echo "End load env with arg: $1"

export NODE_ENV=$NODE_ENV
export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP
export APP_NAME=$APP_NAME
export APP_PATH=$APP_PATH
export REACT_APP_BASE_API_URL=$REACT_APP_BASE_API_URL