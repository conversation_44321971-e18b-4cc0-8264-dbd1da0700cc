import { checkZlpSdkExisted, JSSDKResponse } from "..";

export const openSystemSettings = async () => {
  if (!checkZlpSdkExisted()) {
    throw {
      status: "failed",
      errorCode: "ZLP SDK not existed",
    };
  }
  return window.zlpSdk.Device?.openSystemSettings()
    .then((response: JSSDKResponse<any, any>) => {
      if (!response.data) {
        throw response;
      }
      return response;
    })
    .catch((error: Error) => {
      if (error instanceof Error) {
        return Promise.reject(error);
      }
    });
};
