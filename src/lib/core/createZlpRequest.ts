import { HttpRequestVerbs } from './types';
import {
  defaultHeaderInterceptor,
  maintenanceInterceptor,
  zlpAuthorizeInterceptor,
  zlpResponseUnwrapInterceptor,
} from './interceptors';
import RequestBuilder from './requestBuilder';

export type SuppressError = {
  ignoreErrorCodes?: number[];
};

const createZlpRequest = <ResponseType>( url: string, method: HttpRequestVerbs) => {
  return new RequestBuilder<ResponseType>(url, method)
    .addRequestInterceptors([defaultHeaderInterceptor, zlpAuthorizeInterceptor])
    .addResponseInterceptors([maintenanceInterceptor, zlpResponseUnwrapInterceptor]);
};

export default createZlpRequest;
