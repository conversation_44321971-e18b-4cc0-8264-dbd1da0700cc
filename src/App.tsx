import { RouterProvider } from "react-router-dom";
import router from "./routes";
import FontSizeProvider from "./components/FontSizeProvider";
import { useEffect } from "react";
import { APP_ELEMENT_ID } from "./constants";

const App = () => {
  useEffect(() => {
    document.getElementsByTagName("body")[0].setAttribute("id", APP_ELEMENT_ID);
    return () => {
      document.getElementsByTagName("body")[0].removeAttribute("id");
    };
  }, []);
  return (
    <>
      <FontSizeProvider />
      <RouterProvider router={router} />
    </>
  );
};

export default App;
