import { SVGProps } from "react";

export const LoyaltyIcon = (props?: SVGProps<SVGSVGElement>) => {
    return (
        // biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <rect x="0.5" y="0.984863" width="13" height="13" rx="6.5" stroke="white" />
            <path d="M6.92716 1.48666C6.92716 1.4869 6.92735 1.48708 6.92758 1.48708C8.50306 1.44359 10.0308 2.04233 11.17 3.15084C12.3101 4.25945 12.9687 5.78567 13 7.39072C13 10.74 10.346 13.4612 7.05887 13.483C5.48571 13.5225 3.96194 12.9212 2.82426 11.8143C1.6874 10.7048 1.03048 9.18026 1.00002 7.57857C0.991787 4.23108 3.64231 1.50828 6.92674 1.48625C6.92697 1.48625 6.92716 1.48644 6.92716 1.48666Z" fill="#FFBB33" />
            <path d="M6.94784 3.28623C8.05078 3.2557 9.1203 3.67483 9.91782 4.45085C10.7159 5.22688 11.1769 6.29523 11.1988 7.41877C11.1988 9.76328 9.341 11.6681 7.04004 11.6834C5.93882 11.711 4.87219 11.2901 4.07581 10.5152C3.28001 9.73863 2.82016 8.67145 2.79884 7.55026C2.79308 5.20692 4.6486 3.3009 6.94784 3.28564V3.28623Z" fill="url(#paint0_linear_15875_29623)" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.06916 6.23056C9.06916 6.23056 9.30043 5.11988 9.06916 4.95391C8.8379 4.78795 7.87429 5.40074 7.66872 5.63054C7.45512 5.61298 7.23992 5.61298 7.02632 5.63054C6.79425 5.62655 6.56138 5.6433 6.33252 5.6816C6.12695 5.45181 5.11195 4.83902 4.93208 5.00498C4.75221 5.17094 4.93208 6.28162 4.93208 6.28162C4.37238 6.69972 4.07126 7.37635 4.1355 8.06892C4.27843 9.06391 5.15451 9.7908 6.1655 9.75409H7.88714C8.89571 9.76128 9.7485 9.01364 9.86574 8.01786C9.93079 7.32528 9.62886 6.64866 9.06916 6.23056ZM7.25555 7.73174C7.30614 7.30806 7.6691 6.97932 8.10915 6.97932C8.58372 6.97932 8.96917 7.36152 8.96917 7.83388C8.96917 8.30464 8.58372 8.68763 8.10835 8.68763C7.67472 8.68763 7.31658 8.36847 7.25716 7.95356H6.87975C6.82113 8.36847 6.46218 8.68763 6.02856 8.68763C5.55399 8.68763 5.16935 8.30544 5.16935 7.83308C5.16935 7.36232 5.55399 6.97932 6.02856 6.97932C6.46861 6.97932 6.83157 7.30806 6.88216 7.73174H7.25555ZM8.10943 7.19225C8.46596 7.19225 8.75424 7.47949 8.75424 7.83296C8.75424 8.18723 8.46516 8.47448 8.10943 8.47448C7.7529 8.47448 7.46381 8.18723 7.46381 7.83296L7.46542 7.85371L7.46703 7.84254L7.46622 7.82578C7.46863 7.47471 7.75531 7.19225 8.10863 7.19225H8.10943ZM6.67298 7.83296C6.67298 7.47949 6.3847 7.19225 6.02897 7.19225L6.02817 7.19305C5.67243 7.19305 5.38416 7.47949 5.38416 7.83296C5.38416 8.18643 5.67243 8.47368 6.02817 8.47368C6.3839 8.47368 6.67298 8.18723 6.67298 7.83296ZM5.37825 5.56084L5.34292 5.56563C5.28029 5.58318 5.27386 5.62547 5.29635 5.70446L5.31642 5.7635L5.35818 5.87202C5.41599 6.00367 5.46578 6.02362 5.54849 5.99968C5.63843 5.97415 5.61273 5.85925 5.54849 5.73159L5.48666 5.63983C5.4433 5.57999 5.40636 5.54727 5.34212 5.56563L5.37825 5.56084ZM8.29195 5.7317L8.35298 5.64074V5.63994C8.39634 5.5801 8.43408 5.54739 8.49752 5.56574C8.573 5.58728 8.56658 5.64553 8.52402 5.76362L8.49752 5.83383C8.43328 5.9998 8.38188 6.02533 8.29195 5.9998C8.20201 5.97426 8.22771 5.85937 8.29195 5.7317Z" fill="#FFDB1F" />
            <defs>
                <linearGradient id="paint0_linear_15875_29623" x1="2.74675" y1="3.65434" x2="2.19883" y2="14.6847" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#E25626" />
                    <stop offset="1" stop-color="#FFA200" />
                </linearGradient>
            </defs>
        </svg>

    )
}