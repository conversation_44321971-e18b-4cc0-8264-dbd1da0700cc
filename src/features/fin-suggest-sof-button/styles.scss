@import url("../../styles/colors.css");

.fin-suggest-sof-button {
	/* BASE STYLE */
	--height: 52px;
	--min-width: calc(60% - 16px);
	--bg-color: var(--color-white);
	--text-color: var(--color-primary);
	--border-color: var(--color-primary);
	--border-radius: 8px;
	--padding-x: 8px;
	--padding-y: 8px;
	--justify-content: start;
	--align-items: center;
	--gap: 8px;
	--border-width: thin;
	--border-style: solid;

	--icon-width: 36px;
	--icon-height: 36px;

	--title-font-size: 12px;
	--tilte-font-weight: 400;
	--tilte-line-height: 16px;

	--amount-font-size: 14px;
	--amount-font-weight: 700;
	--amount-line-height: 18px;

	--origin-amount-font-size: 12px;
	--origin-amount-font-weight: 400;
	--origin-amount-line-height: 16px;

	--error-message-font-size: 12px;
	--error-message-font-weight: 400;
	--error-message-line-height: 16px;
	--error-message-color: var(--color-red-500);

	--promotion-badge-text-font-size: 12px;
	--promotion-badge-text-font-weight: 400;
	--promotion-badge-text-line-height: 16px;
	--promotion-badge-text-color: var(--color-white);
	--promotion-badge-text-background-color: var(--color-secondary-red);
	/* END BASE STYLE */

	/* LOADING STYLE */
	--loading-border-color: var(--color-dark-100);
	/* END LOADING STYLE */

	/* DISABLE STYLE */
	--disabled-text-color: var(--color-dark-200);
	--disabled-border-color: var(--color-dark-200);
	/* END DISABLE STYLE */

	min-height: var(--height);
	height: var(--height);
	min-width: var(--min-width);
	box-sizing: border-box;
	position: relative;
	color: var(--text-color);
	background-color: var(--bg-color);
	border-radius: var(--border-radius);
	border: var(--border-width) var(--border-style) var(--border-color);
	padding: var(--padding-y) var(--padding-x);
	padding-right: 0;
	justify-content: var(--justify-content);
	align-items: var(--align-items);
	gap: var(--gap);
	display: flex;

	/* Fade-in animation */
	opacity: 0;
	animation: fadeIn 0.3s ease-in-out forwards;

	&[data-loading="true"] {
		border-color: var(--loading-border-color);
		/* Don't animate when loading */
		animation: none;
		opacity: 1;
	}

	&:disabled {
		color: var(--disabled-text-color);
		border-color: var(--disabled-border-color);
	
		img {
			filter: grayscale(80%)
		}
	}

	&__icon {
		width: var(--icon-width);
		height: var(--icon-height);

		img {
			object-fit: contain;
		}
	}

	&__content {
		max-width: calc(100% - (var(--padding-x) + var(--icon-width)));
	}

	&__title {
		font-size: var(--title-font-size);
		font-weight: var(--tilte-font-weight);
		line-height: var(--tilte-line-height);
		text-align: start;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		width: 100%;
	}

	&__amount {
		display: flex;
		flex-direction: row;
		align-items: end;
		gap: 4px;
		font-size: var(--amount-font-size);
		font-weight: var(--amount-font-weight);
		line-height: var(--amount-line-height);
		text-align: start;
	}

	&__origin-amount {
		font-size: var(--origin-amount-font-size);
		font-weight: var(--origin-amount-font-weight);
		line-height: var(--origin-amount-line-height);
		text-decoration-line: line-through;
		text-align: start;
	}

	&__error-message {
		font-size: var(--error-message-font-size);
		font-weight: var(--error-message-font-weight);
		line-height: var(--error-message-line-height);
		text-align: start;
		color: var(--error-message-color);
		padding: 0 8px 0 0;
	}



	&__promotion-badge-text {
		font-size: var(--promotion-badge-text-font-size);
		font-weight: var(--promotion-badge-text-font-weight);
		line-height: var(--promotion-badge-text-line-height);
		background-color: var(--promotion-badge-text-background-color);
		color: var(--promotion-badge-text-color);
		text-align: start;
		display: flex;
		align-items: center;
		gap: 4px;
		height: 18px;
		padding: 2px 4px;
		border-radius: 4px 4px 0px 4px;
		font-weight: 700;
	}

	&__promotion-badge {
		position: absolute;
		z-index: 1;
		top: -12px;
		right: -2px;
		height: auto;

		::before {
			content: "";
			position: absolute;
			bottom: -2px;
			right: 0;
			width: 2px;
			height: 2px;
			background-color: var(--promotion-badge-text-background-color);
			z-index: -1;
			clip-path: polygon(0 0, 100% 0, 0% 100%, 0 100%);
		}
	}

	&__icon-skeleton {
		width: var(--icon-width);
		height: var(--icon-height);
		background: linear-gradient(91.83deg, #F0F6FF 0.64%, #E8F0FD 64.81%, #EEF4FE 100%);
		border-radius: 50%;
	}

	&__content-wrapper-skeleton {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 4px;
		padding-right: 8px;
	}
	&__title-skeleton {
		width: 65%;
		height: var(--title-font-size);
		background: linear-gradient(91.83deg, #F0F6FF 0.64%, #E8F0FD 64.81%, #EEF4FE 100%);
		border-radius: 4px;
	}

	&__amount-skeleton {
		width: 100%;
		height: var(--amount-font-size);
		background: linear-gradient(91.83deg, #F0F6FF 0.64%, #E8F0FD 64.81%, #EEF4FE 100%);
		border-radius: 4px;
		
	}
}

.animate-shimmer {
	--animate-time: 1s;
	animation: shimmer var(--animate-time, 1s);
	animation-iteration-count: infinite;
}

@keyframes shimmer {
	from {
		background-position: -100px 0;
	}

	to {
		background-position: 100px 0;
	}
}

.animate-marquee-left {
	--animate-time: 1700ms;
	animation: marquee-left var(--animate-time, 5s) linear infinite;
}

@keyframes marquee-left {
	0% {
		transform: translateX(0);
	}
	50% {
		transform: translateX(-50%);
	}
	100% {
		transform: translateX(0);
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
		filter: blur(4px);
	}
	to {
		opacity: 1;
		filter: blur(0);
	}
}