#!/usr/bin/env bash
 
HOME=$(pwd)
 
# Extract version value from package.json
date=$(date '+%d%m%Y.%H%M')
npm_package_version="$(node -p "require('./package.json').version")-${date}"
echo ENV: npm_package_version: ${npm_package_version}
if [ ! -z $2 ]; then
  npm_package_version=$2
fi

# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi
 
if [ $1 == "pro" ]; then
  # Load .env.product file
  . "${HOME}/.env.product"
fi
 
# ToDo switch environment when build time
# if [ ! -z ${VAR_ENV} ]; then
#   if [ ${VAR_ENV} == "qc" ]; then
#     # Load .env.develop file
#     . "${HOME}/.env.develop"
#   elif [ ${VAR_ENV} == "stg" ]; then
#     # Load .env.staging file
#     . "${HOME}/.env.staging"
#   elif [ ${VAR_ENV} == "pro" ]; then
#     # Load .env.product file
#     . "${HOME}/.env.production"
#   fi
# fi
echo "========== BEGIN LOAD ENV VARS $1 ============="
echo "NODE_ENV: ${NODE_ENV}"
echo "npm_package_version: ${npm_package_version}"
echo "PUBLIC_URL: ${PUBLIC_URL}"
echo "GENERATE_SOURCEMAP: ${GENERATE_SOURCEMAP}"
echo "APP_ID: ${APP_ID}"
echo "APP_NAME: ${APP_NAME}"
echo "APP_PATH: ${APP_PATH}"
echo "BUID_DIR: ${BUILD_DIR}"
echo "REACT_APP_ENV: ${REACT_APP_ENV}"
echo "========== END LOAD ENV VARS =========="
 
# export CDN variables
export APP_ID=$APP_ID
export APP_NAME=$APP_NAME
export BUILD_DIR=$BUILD_DIR
export CONFIG_TOOL_API_URL=$CONFIG_TOOL_API_URL
export CDN_USERNAME=$CDN_USERNAME
export CDN_SERVER_IP=$CDN_SERVER_IP
export CDN_SERVER_DIR=$CDN_SERVER_DIR
 
# export variables environent
export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP
export NODE_ENV=$NODE_ENV