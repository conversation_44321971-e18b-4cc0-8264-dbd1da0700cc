import { defineConfig, loadEnv } from "@rsbuild/core";
import { pluginReact } from "@rsbuild/plugin-react";
import { pluginSass } from "@rsbuild/plugin-sass";

import path from "node:path";
import packageJson from "./package.json";
import moduleFedetationsConfig from "./module-fedetations.config";

const deps = packageJson.dependencies;
const { publicVars } = loadEnv({ prefixes: ["REACT_APP_"] });
console.log("RSBUILD: NODE_ENV:", process.env.NODE_ENV);
console.log("RSBUILD: publicVars", publicVars) 
export default defineConfig({
  mode: process.env.NODE_ENV === "development" ? "development" : "production",
  plugins: [pluginReact(), pluginSass()],
  tools: {
    rspack: {
      output: {
        uniqueName: "remote",
        publicPath: `${process.env.PUBLIC_URL || ""}/`,
      },
    },
  },
  source: {
    entry: {
      index: "./src/index.tsx",
    },
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    define: {
      ...publicVars,
      __DEV__: process.env.NODE_ENV === "development",
    },
  },
  output: {
    sourceMap: {
      js: "source-map",
      css: true,
    },
  },
  moduleFederation: moduleFedetationsConfig,
  performance: {
    chunkSplit: {
      strategy: "split-by-experience",
    },
    bundleAnalyze: {
      analyzerMode: "static",
      openAnalyzer: false,
      // Distinguish by environment names, such as `web`, `node`, etc.
      reportFilename: `report.html`,
    },
  },
});
