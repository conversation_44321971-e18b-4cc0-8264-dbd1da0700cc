export const customAuthorizeInterceptor = async (request: RequestInit) => {
  let authenHeaders: any = {};

  const zlpTokenFromLS  = localStorage.getItem('zlp_token');
  if (zlpTokenFromLS) {
    authenHeaders = {
      ...authenHeaders,
      Authorization: `Bearer ${zlpTokenFromLS}`,
    };
  }

  return {
    ...request,
    headers: {
      ...request.headers,
      ...authenHeaders,
    },
  };
};
