export enum LocationErrorCode {
    ZPI_LOCATION_PERMISSION_DENIED = '010101', 
    ZPI_LOCATION_INFO_UNAVAILABLE_ZPI = '010102',
    ZPI_LOCATION_REQUEST_TIMEOUT = '010103',
    ZMP_LOCATION_USER_DENY_REQUEST = '010104',
    ZMP_LOCATION_USER_FORCE_DENIED = '010105',
    ZMP_LOCATION_NEED_AUTHEN_REQUEST = '010106',
    ZMP_LOCATION_USER_DENIED = '010107',
    ZPA_LOCATION_PERMISSION_NOT_GRANTED = '161001',
    ZPA_LOCATION_PERMISSION_DENIED = '161002',
    ZPA_LOCATION_USER_DENIED = '161003',
    ZPA_LOCATION_NEED_INPUT_APP_ID = '161004',
}

/**
 * @typedef {Object} GetLocationOptions
 * @property {('high'|'medium'|'low')} [accuracy] ZPA only. 
 *   - Specifies the desired accuracy level for location.
 *   - high: Most precise (1-10m). Used for navigation, ride-sharing. High battery usage.
 *   - medium: Balanced (10-100m). For social apps, local search. Moderate battery usage.
 *   - low: Least precise (>100m). For regional content. Low battery usage. 
 * @property {boolean} [isCustom]  ZPA only.
 * - When true, allows custom location UX. When false (default),
 *   includes built-in permission handling.
 * @property {number} [appId] - App ID provided by Zalopay team when registering as merchant.
 */
export type GetLocationOptions = {
    accuracy?: 'high' | 'medium' | 'low';
    isCustom?: boolean;
    appId?: number;
}

export type LocationData = {
    latitude: number;
    longitude: number;
    city: string;
    district: string;
    ward: string;
    street: string;
    countryCode: string;
    accuracy: string;
    timestamp: string
}

type ValueOf<T> = T[keyof T];


export type CheckLocationPermissionData = {
    permissionStatus: ValueOf<typeof PermissionStatus> | string;
}

export enum PermissionStatus {
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    PERMISSION_NOT_GRANTED = 'PERMISSION_NOT_GRANTED',
    PERMISSION_ALWAYS = 'PERMISSION_ALWAYS',
    PERMISSION_WHEN_USE = 'PERMISSION_WHEN_USE',
}

export type RequestLocationPermissionOptions = {
    accuracy?: 'high' | 'medium' | 'low';
}

export type RequestLocationPermissionData = {
    permissionStatus: ValueOf<typeof PermissionStatus> | string;
}


