# FinSuggestSOFButton Component

A React component that displays financial service offers (Pay Later, Installment, Mini Pay Later) with promotional badges and handles user interactions for selecting source of funding (SOF) options. Built with provider pattern architecture for clean separation of concerns and flexible fallback handling.

## Architecture

The component follows a provider pattern with clear separation:

```
FinSuggestSOFButton (Provider)
├── FinSuggestSOFButtonUI - Pure UI button component
├── useSuggestSOFButton - Custom hook for API logic and state management
└── Fallback handling at provider level
```

## Features

- **🏗️ Provider Pattern**: Clean separation between logic and UI components
- **🔄 Fallback Support**: Custom React nodes when offers are unavailable
- **🚀 Automatic API Integration**: Fetches payment offers from fin-lending API on mount
- **🎯 Smart Caching**: Response caching with payload-based keys for performance
- **⏱️ Latency Control**: Configurable timeout thresholds for API requests
- **🏷️ Promotional Badges**: Displays discount, cashback, or loyalty coin promotions
- **💀 Loading States**: Skeleton loading animation with shimmer effects
- **🛡️ Error Handling**: Graceful error states with fallback messaging
- **🎨 Customizable Styling**: CSS variables for theming and custom styles
- **📱 Marquee Animation**: Scrolling text for long content with configurable timing
- **📘 TypeScript Support**: Full type definitions and strict typing
- **🧪 Comprehensive Testing**: Unit tests with Vitest and React Testing Library

## Installation

### Module Federation

The FinSuggestSOFButton component is available through Module Federation for different environments:

#### Development Environment
```javascript
// webpack.config.js or module federation configuration
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      remotes: {
        fin_sdk: 'https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7'
      }
    })
  ]
};
```

#### Usage in your application
```tsx
// Import the component from the federated module
import FinSuggestSOFButton from 'fin_sdk/FinSuggestSOFButton';
```

#### Environment URLs

| Environment | Status | Module Name | Component Path | Remote URL |
|-------------|--------|-------------|----------------|------------|
| **dev** | ✅ Available | `fin_sdk` | `./FinSuggestSOFButton` | `https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7` |
| **stg** | 🚧 WIP | `fin_sdk` | `./FinSuggestSOFButton` | Coming soon |
| **prod** | 🚧 WIP | `fin_sdk` | `./FinSuggestSOFButton` | Coming soon |

#### Notes
- Currently only the development environment is available
- Staging and production environments are work in progress
- Make sure your webpack configuration supports Module Federation
- The component path within the federated module is `./FinSuggestSOFButton`

### Local Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Run tests with coverage
npm run coverage
```

## Basic Usage

```tsx
import FinSuggestSOFButton from '@/features/fin-suggest-sof-button';
import type { SuggestSOFCallbackData, OrderItem } from '@/features/fin-suggest-sof-button';

const orders: OrderItem[] = [
  { app_id: 123, amount: 100000 },
  { app_id: 456, amount: 50000 }
];

const handleOfferSelect = (result?: SuggestSOFCallbackData) => {
  if (result) {
    console.log('Selected offer:', result);
    // Handle the selected offer
    // result contains: { specified_sof: { pmc_id: number, partner_code: string } }
  } else {
    console.log('No offer available');
  }
};

const handleInitialData = (data: SuggestOfferResponse) => {
  console.log('Initial offer data:', data);
  // Optional: Handle initial data when offers are first loaded
};

const handleError = (error: string) => {
  console.error('Error fetching offers:', error);
  // Optional: Handle errors (e.g., show notification, log to analytics)
};

function MyComponent() {
  return (
    <FinSuggestSOFButton
      orders={orders}
      onPress={handleOfferSelect}
      onInitial={handleInitialData}
      onErrorCallback={handleError}
      options={{
        specific_sof: "PAYLATER", // Optional: specify SOF type
        latency: 5000 // Optional: 5 second timeout
      }}
    />
  );
}
```

## Props

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `orders` | `OrderItem[]` | Array of order items with app_id (number) and amount |
| `onPress` | `(result?: SuggestSOFCallbackData) => void` | Callback when user selects an offer (result can be undefined) |

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fallback` | `ReactNode` | - | Custom React node to render when offer is unavailable |
| `onInitial` | `(data: SuggestOfferResponse) => void` | - | Callback when initial offer data is loaded |
| `onErrorCallback` | `(error: string) => void` | - | Callback when an error occurs during offer fetching |
| `children` | `ReactNode` | - | Custom content to display instead of default layout |
| `disabled` | `boolean` | `false` | Disable the button |
| `className` | `string` | - | Additional CSS classes |
| `options` | `object` | - | Configuration options (see Options section) |

### Options Object

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `specific_sof` | `string` | - | Specify SOF type: "PAYLATER", "INSTALLMENT", "MINI_PAYLATER", "SOF_UNSPECIFIED" |
| `latency` | `number` | - | Maximum response time in milliseconds before throwing timeout error |

All standard HTML button attributes are also supported.

## Types

### OrderItem
```tsx
type OrderItem = {
  app_id: number;  // Application ID as number
  amount: number;  // Order amount in VND
};
```

### SuggestSOFCallbackData
```tsx
type SuggestSOFCallbackData = {
  specified_sof: {
    pmc_id: number;        // Payment method code ID
    partner_code: string;  // Partner identifier
  };
};
```

### SuggestOfferResponse
```tsx
type SuggestOfferResponse = {
  offers?: Array<Offer>;  // Array of available offers (optional)
  issued_at: string;      // ISO timestamp when response was issued
};
```

### Offer
```tsx
type Offer = {
  id: string;
  pmc_id: number;
  status: "OFFER_STATUS_AVAILABLE" | "OFFER_STATUS_UNAVAILABLE";
  product_code: "PAYLATER" | "INSTALLMENT" | "MINI_PAYLATER";
  partner_code: string;
  total_order_amount: string;
  total_charge_amount: string;
  fee_details: OfferFeeDetail;
  promo_details: OfferPromotionDetail;
  display_info: OfferDisplayInfo;
  paylater_details: Record<string, unknown>;
  installment_details: Record<string, unknown>;
};
```

## API Integration

The component automatically fetches payment offers from the fin-lending API:

- **Endpoint**: `https://{env}fin-lending.zalopay.vn/asset-management/v1/payment-offers/consult`
- **Method**: POST
- **Environment Detection**: Automatically detects dev/stg/prod based on hostname
- **Caching**: Responses are cached using payload-based keys for performance
- **Debouncing**: API calls are debounced by 500ms to prevent excessive requests

### Request Payload
```tsx
{
  orders: Array<{ app_id: number; amount: number }>;
  specific_sof?: string; // Optional SOF type filter
}
```

## Fallback Functionality

The provider returns the fallback when:
- No offer is available (`!displayOffer`)
- Offer status is not "OFFER_STATUS_AVAILABLE" (`!isOfferAvailable`)
- An error occurred during API call (`!!error`)
- AND `fallback` prop is provided

### Basic Fallback Usage
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  fallback={<div>Payment option not available</div>}
/>
```

### Custom Fallback Component
```tsx
const CustomFallback = () => (
  <div className="payment-unavailable">
    <span>🚫</span>
    <span>No payment options available</span>
    <button onClick={() => window.location.href = '/payment'}>
      Use Traditional Payment
    </button>
  </div>
);

<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  fallback={<CustomFallback />}
/>
```

### Without Fallback (Default Behavior)
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  // No fallback prop - uses internal ErrorContent within button
/>
```

## Advanced Usage

### Custom Content
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
>
  <div className="custom-content">
    <span>Custom offer display</span>
  </div>
</FinSuggestSOFButton>
```

### Latency Control
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  options={{
    latency: 3000 // Throw error if API takes longer than 3 seconds
  }}
  onErrorCallback={(error) => {
    if (error.includes('timeout')) {
      // Handle timeout specifically
      console.log('API is too slow, showing fallback');
    }
  }}
/>
```

### Specific SOF Type
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  options={{
    specific_sof: "PAYLATER" // Only show Pay Later offers
  }}
/>
```

## Styling

The component uses CSS variables for theming. You can customize these in your CSS:

```css
.fin-suggest-sof-button {
  --primary-bg-color: #your-color;
  --primary-text-color: #your-text-color;
  --primary-ring-color: #your-ring-color;
  --promotion-badge-text-background-color: #your-badge-color;
  --color-secondary-red: #your-red-color;
  --color-primary-green: #your-green-color;
  --color-secondary-orange: #your-orange-color;
}
```

### Animation Customization
```css
.fin-suggest-sof-button {
  --animate-time: 5s; /* Skeleton animation duration */
}

/* Marquee text animation timing */
.fin-suggest-sof-button__content {
  /* Error messages: 2700ms duration, 1000ms delay */
  /* Normal content: 700ms duration, 1000ms delay */
}
```

## Button States

The component handles several states automatically:

- **Loading**: Shows skeleton animation with shimmer effects while fetching offers
- **Error**: Displays error message when API fails or times out
- **No offers**: Shows "Không có đề xuất trả sau cho đơn hàng này" message
- **Disabled**: Button is disabled when no valid offers, has errors, or is explicitly disabled
- **Active**: Normal interactive state with offer data and promotional badges

## Promotion Types

The component supports three promotion types with distinct styling:

- `PROMOTION_TYPE_DISCOUNT`: Shows "Giảm {amount}" with red badge (`--color-secondary-red`)
- `PROMOTION_TYPE_CASHBACK`: Shows "Hoàn {amount}" with green badge (`--color-primary-green`)
- `PROMOTION_TYPE_LOYALTY_COIN`: Shows "+{amount} xu" with orange badge (`--color-secondary-orange`) and loyalty coin icon

## Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import FinSuggestSOFButton from '@/features/fin-suggest-sof-button';

test('calls onPress when button is clicked', () => {
  const handleClick = jest.fn();
  const orders = [{ appId: "123", amount: 100 }];
  
  render(
    <FinSuggestSOFButton 
      orders={orders} 
      onPress={handleClick}
    />
  );
  
  const button = screen.getByRole('button');
  fireEvent.click(button);
  
  expect(handleClick).toHaveBeenCalled();
});
```