import { Link } from "react-router-dom";

export default function HomeScreen() {
  return (
    <div className="flex flex-col items-start justify-start min-h-screen p-8 space-y-2 bg-gray-50">
      <Link 
        viewTransition
        to="/fin-suggest-sof-button" 
        className="w-full px-6 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
      >
        Fin Suggest SOF Button
      </Link>
       <Link 
        viewTransition
        to="/ai" 
        className="w-full px-6 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
      >
        AI
      </Link>
      <Link 
        viewTransition
        to="/location-screen" 
        className="w-full px-6 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
      >
        Location Picker
      </Link>
    </div>
  );
}