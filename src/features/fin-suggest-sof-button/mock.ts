import { OfferStatus, SuggestOfferResponse } from "./types";

export const mockOfferResponse: SuggestOfferResponse = {
	offers: [
		{
			id: "offer123",
			pmc_id: 1001,
			status: OfferStatus.OFFER_STATUS_AVAILABLE,
			product_code: "PROD001",
			partner_code: "CIMB",
			total_order_amount: "1500",
			total_charge_amount: "1600",
			fee_details: {
				total_amount: "100",
				fee_items: [
					{
						type: 1,
						amount: "50",
					},
					{
						type: 2,
						amount: "50",
					},
				],
			},
			promo_details: {
				total_amount: "200",
				promo_items: [
					{
						type: "PROMOTION_TYPE_CASHBACK",
						amount: "100",
					},
				],
				type: ""
			},
			display_info: {
				icon_url:
					"https://simg.zalopay.com.vn/zst/zpi/images/revamp-home-3/v1/service_finance_installment.svg",
				origin_amount_text: "1.480.000đ",
				charge_amount_text: "1.500.000đ",
				payment_term_text: "Trả sau 37 ngày",
				error_description: "",
			},
			paylater_details: {
				enabled: true,
				available_limit: "5000",
			},
			installment_details: {
				terms: [3, 6, 12],
				interest_rate: "1.5%",
			},
		},
	],
	issued_at: "2025-05-22T07:48:34.334Z",
};
