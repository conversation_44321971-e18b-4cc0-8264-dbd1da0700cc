/* eslint-disable no-undef */
// import get from 'lodash.get';

import RequestBuilder from '../requestBuilder';
import { HttpResponseCode } from '../types';

export const maintenanceInterceptor = async ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => {
  if (response.status === HttpResponseCode.SERVICE_UNAVAILABLE) {

    //const errorBody = await response.clone()?.json();
    //const reason = get(errorBody, 'reason');
    // if (reason === "RESOURCE_UNDER_MAINTENANCE") {

    //   const maintenanceService = Object.values(MaintenanceService).find(type =>
    //     RequestBuilder.url.includes(type)
    //   );

    //   if (maintenanceService) {
    //     appStore.getState().setMaintananceInfo(MAINTENANCE_INFO[maintenanceService]);
    //   }
    // }

  } else if (
    response.status !== HttpResponseCode.SERVICE_UNAVAILABLE
    //&&
    //WIP
    // store.getState()?.globalReducer?.is_maintenance
  ) {
    //WIP
    // store.dispatch(setIsMaintenance(false));
  }
  return { response, RequestBuilder };
};
