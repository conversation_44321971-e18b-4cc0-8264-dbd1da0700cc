import { useCallback } from "react";
import { trackEvent as ZLPTrackEvent } from "@/lib/zlpSdk/tracking/trackEvent";
import isZPA from "@/lib/isZPA";
import { utmStore } from "@/store/utmStore";

export const useTracking = (screenId: string) => {
  const utmSource = utmStore(state => state.utmSource);

  const platformTrackingId = isZPA ? "01" : "02";
  
  const trackEvent = useCallback(
    (eventId: string, metadata: Record<string, any> = {}, ...params: any[]) => {
      ZLPTrackEvent(`${platformTrackingId}.${screenId}.${eventId}`, metadata, ...params, utmSource);
    },
    [screenId, utmSource, platformTrackingId, ],
  );

  return { trackEvent };
};
