
const ENDPOINT = "fin-lending.zalopay.vn";
const VERSION = 1;
const QC_HOSTS = ["socialdev.zalopay.vn", "localhost"];
const STG_HOSTS = ["socialstg.zalopay.vn"];

const GROUP = "asset-management";
const PATH = "payment-offers/consult";

const CDN_URL = "https://simg.zalopay.com.vn";
const CDN_PATH = "fs/fin-sdk/images";
const CDN_URL_WITH_PATH = `${CDN_URL}/${CDN_PATH}`;

const ASSETS = {
	PAYLATER: `${CDN_URL_WITH_PATH}/service_finance_paylater.svg`,
	INSTALLMENT: `${CDN_URL_WITH_PATH}/service_finance_installment.svg`,
	MINI_PAYLATER: `${CDN_URL_WITH_PATH}/service_finance_paylater.svg`,
}

const ASSETS_DARK_MODE = {
	PAYLATER: `${CDN_URL_WITH_PATH}/service_finance_paylater.svg`,
	INSTALLMENT: `${CDN_URL_WITH_PATH}/service_finance_installment.svg`,
	MINI_PAYLATER: `${CDN_URL_WITH_PATH}/service_finance_paylater.svg`,
}

const withBaseUrl = ({
	path,
}: {
	endpoint?: string;
	path: string;
}): string => {
	let env = "";
	const hostName = window.location.hostname;
	if (QC_HOSTS.includes(hostName)) {
		env = "dev";
	} else if (STG_HOSTS.includes(hostName)) {
		env = "stg";
	}
	return `https://${env}${ENDPOINT}/${GROUP}/v${VERSION}/${path}`;
};

const getAssetUrl = ({ asset, isDarkMode }: { asset: string, isDarkMode: boolean }) => {
	return isDarkMode ? ASSETS_DARK_MODE[asset as keyof typeof ASSETS_DARK_MODE] : ASSETS[asset as keyof typeof ASSETS];
}

export { ENDPOINT, PATH, withBaseUrl, ASSETS, ASSETS_DARK_MODE, getAssetUrl };
