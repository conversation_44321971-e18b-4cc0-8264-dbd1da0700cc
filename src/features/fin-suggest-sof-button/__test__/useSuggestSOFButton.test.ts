import { renderHook, act } from '@testing-library/react-hooks';
import { vi } from 'vitest';
import { useSuggestSOFButton } from '../useSuggestSOFButton';
import * as api from '../api';

const mockOffer = {
  offers: [
    {
      id: 'offer1',
      pmc_id: 123,
      status: 'OFFER_STATUS_AVAILABLE',
      product_code: 'PAYLATER',
      partner_code: 'partnerABC',
      total_order_amount: '1000',
      total_charge_amount: '1100',
      fee_details: {
        total_amount: '100',
        fee_items: [
          { type: 1, amount: '100' }
        ]
      },
      promo_details: {
        optimal_promo: {
          type: 'PROMOTION_TYPE_DISCOUNT',
          amount: '50',
          id: 'promo1',
        }
      },
      display_info: {
        icon_url: '',
        origin_amount_text: '',
        charge_amount_text: '',
        payment_term_text: '',
        error_description: '',
      },
      paylater_details: {},
      installment_details: {},
    }
  ],
  issued_at: '2025-06-23T00:00:00Z',
};

const baseProps = {
  options: { specific_sof: 'VISA' },
  orders: [],
  onPress: vi.fn(),
};

describe('useSuggestSOFButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should call onInitial and set suggestOfferResponse on success', async () => {
    vi.spyOn(api, 'postConsultPaymentOffer').mockResolvedValueOnce(mockOffer);
    const onInitial = vi.fn();
    const props = { ...baseProps, onInitial };
    const { result, waitForNextUpdate } = renderHook(() => useSuggestSOFButton(props));
    await waitForNextUpdate();
    expect(result.current.suggestOfferResponse).toEqual(mockOffer);
    expect(onInitial).toHaveBeenCalledWith(mockOffer);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeUndefined();
  });

  it('should set error and call onErrorCallback on failure', async () => {
    vi.spyOn(api, 'postConsultPaymentOffer').mockRejectedValueOnce(new Error('fail'));
    const onErrorCallback = vi.fn();
    const props = { ...baseProps, onErrorCallback };
    const { result, waitForNextUpdate } = renderHook(() => useSuggestSOFButton(props));
    await waitForNextUpdate();
    expect(result.current.error).toBe('fail');
    expect(onErrorCallback).toHaveBeenCalledWith('fail');
    expect(result.current.isLoading).toBe(false);
  });

  it('should call onPress with correct data when _handleOnClick is called and offer exists', async () => {
    vi.spyOn(api, 'postConsultPaymentOffer').mockResolvedValueOnce(mockOffer);
    const onPress = vi.fn();
    const props = { ...baseProps, onPress };
    const { result, waitForNextUpdate } = renderHook(() => useSuggestSOFButton(props));
    await waitForNextUpdate();
    act(() => {
      result.current._handleOnClick();
    });
    expect(onPress).toHaveBeenCalledWith({
      specified_sof: {
        pmc_id: 123,
        partner_code: 'partnerABC',
      },
    });
  });

  it('should call onPress with undefined if no offer exists', async () => {
    vi.spyOn(api, 'postConsultPaymentOffer').mockResolvedValueOnce({ offers: [], issued_at: '2025-06-23T00:00:00Z' });
    const onPress = vi.fn();
    const props = { ...baseProps, onPress };
    const { result, waitForNextUpdate } = renderHook(() => useSuggestSOFButton(props));
    await waitForNextUpdate();
    act(() => {
      result.current._handleOnClick();
    });
    expect(onPress).toHaveBeenCalledWith(undefined);
  });

  it('should not call _handleOnClick if loading', async () => {
    vi.spyOn(api, 'postConsultPaymentOffer').mockResolvedValueOnce(mockOffer);
    const onPress = vi.fn();
    const props = { ...baseProps, onPress };
    const { result } = renderHook(() => useSuggestSOFButton(props));
    act(() => {
      result.current._handleOnClick();
    });
    expect(onPress).not.toHaveBeenCalled();
  });

  it('should clear previous timeout when debouncedInitial is called multiple times', async () => {
    vi.useFakeTimers();
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
    vi.spyOn(api, 'postConsultPaymentOffer').mockResolvedValueOnce(mockOffer);
    const props = { ...baseProps };
    const { result } = renderHook(() => useSuggestSOFButton(props));
    // Call debouncedInitial twice quickly
    act(() => {
      result.current.debouncedInitial();
      result.current.debouncedInitial();
    });
    expect(clearTimeoutSpy).toHaveBeenCalled();
    vi.useRealTimers();
  });
});
