import { Logger } from "../../logger";

export enum ETrackingStatus {
  ERROR = "error",
  SUCCESS = "success",
}
export interface ITrackingResult {
  status: ETrackingStatus;
  errorCode?: string;
  errorMessage?: string;
}

export interface TrackingParameter {
    eventId?: string,
    metadata?: object,
    utmCampaign?: string,
    utmMedium?: string,
    utmScheme?: string,
    utmSource?: string,
    utmContent?: string,
    utmTerm?: string,
    campaign?: string,
    fromMenu?: string,
    fromSource?: string,
    sourceId?: string,
}
const trackingLogger = new Logger("TRACKING");

export const trackEvent = async (eventId: string, metadata?: Record<string, any>, ...params: any[] ): Promise<ITrackingResult> => {
  if (window.zlpSdk?.Tracking?.trackEvent) {
    window.zlpSdk?.Tracking?.trackEvent?.({
      eventId,
      metadata,
      ...params,
    }).then((result: ITrackingResult) => {
      trackingLogger[result.status === ETrackingStatus.ERROR ? "error" : "info"](eventId, { metadata, result });
      return result;
    });
  }

  return {
    status: ETrackingStatus.ERROR,
    errorCode: "",
    errorMessage: "ZLP SDK not found",
  } as ITrackingResult;
};
