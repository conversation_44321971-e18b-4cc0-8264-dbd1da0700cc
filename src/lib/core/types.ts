export enum ApiStatus {
  success = 0,
  fail = 1,
}

export enum HttpRequestVerbs {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  PATCH = "PATCH",
  DELETE = "DELETE",
}

export enum HttpResponseCode {
  AUTHEN_FAILED = 401,
  SUCCESS = 200,
  SERVICE_UNAVAILABLE = 503,
  TOO_MANY_REQUEST = 429,
}

export enum ServiceResponseSubcode {
  SUCCESS = 0,
  INVALID_USER_PROFILE = 105,
  TO0_MANY_REQUESTS = 401,
  INVALID_CORP_BALANCE = 508,
}