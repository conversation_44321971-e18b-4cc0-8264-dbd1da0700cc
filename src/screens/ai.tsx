import IphoneMockup from "@/components/iphone-mockup";
import PlusMenu from "@/features/plus-menu";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

const AI = () => {
  const isMobile = useIsMobile();
  const Wrapper = isMobile ? "div" : IphoneMockup;

  return (
    <>
      <div className="w-full min-h-screen bg-gray-50">
        <div className={cn({ "max-h-[812px]": !isMobile })}>
          <Wrapper
            fullScreen
            className="aspect-[8/16] w-full max-w-[430px] overflow-hidden mx-auto relative h-screen bg-white"
          >
            <div className="relative flex flex-col h-full overflow-hidden"></div>
          </Wrapper>
        </div>
      </div>
      <PlusMenu />
    </>
  );
};

export default AI;
