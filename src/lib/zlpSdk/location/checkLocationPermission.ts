import { checkZlpSdkExisted, type JSSDKResponse } from "..";
import type { CheckLocationPermissionData } from "./type";

export const checkLocationPermission = async () => {
  if (!checkZlpSdkExisted()) {
    throw {
      status: "failed",
      errorCode: "ZLP SDK not existed",
    };
  }
  return window.zlpSdk.Location?.checkLocationPermission()
    .then((response: JSSDKResponse<CheckLocationPermissionData, any>) => {
      return response;
    })
    .catch((error: Error) => {
      if (error instanceof Error) {
        return Promise.reject(error);
      }
    });
};
