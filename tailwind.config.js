const defaultTheme = require("tailwindcss/defaultTheme");
import { colors } from "./src/constants/colors";
import { extendTheme } from "./src/constants/extendTheme";


/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,js,jsx,tsx}", "./src/**/*.{html,js,jsx,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      ...defaultTheme,
      colors,
      ...extendTheme,
    },
  },
  plugins: [],
};
