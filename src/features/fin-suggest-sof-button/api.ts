import createZlpRequest from "@/lib/core/createZlpRequest";
import { PATH, withBaseUrl } from "./constants";
import type { SuggestOfferResponse } from "./types";
import { HttpRequestVerbs } from "@/lib/core/types";
import { customAuthorizeInterceptor } from "@/lib/core/interceptors/customAuthorizeInterceptor";
import { finSuggestSOFButtonHeaderInterceptor } from "./finSuggestSOFButtonHeaderInterceptor";


export const postConsultPaymentOffer = async (payload: {
  orders: Array<{ app_id: number; amount: number }>;
}) =>
  createZlpRequest<SuggestOfferResponse>(
    withBaseUrl({ path: PATH }),
    HttpRequestVerbs.POST
  )
    .addRequestInterceptors([customAuthorizeInterceptor, finSuggestSOFButtonHeaderInterceptor])
    .setHeaders({
      "Content-Type": "application/json",
    })
    .setDataBody(JSON.stringify(payload))
    .build();
