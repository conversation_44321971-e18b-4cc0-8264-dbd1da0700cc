import {
  checkLocationPermission,
  getLocation,
  GetLocationOptions,
  PermissionStatus,
  requestLocationPermission,
} from "@/lib/zlpSdk/location";
import { openSystemSettings } from "@/lib/zlpSdk/device";
import { CityModel, getUserLocation } from "./api";

export type CurrentLocation = {
  permissionStatus: PermissionStatus;
  shouldOpenSetting: boolean;
  location?: CityModel;
};

export const useLocation = () => {
  const getCurrentLocation = async (
    options?: GetLocationOptions
  ): Promise<CurrentLocation | null> => {
    try {
      // Check permission status first
      const permissionStatus = await checkUserLocationPermission();
      if (!permissionStatus.isGranted) {
        if (permissionStatus.isDenied) {
          // User has explicitly denied permission
          return {
            permissionStatus: permissionStatus.status,
            shouldOpenSetting: true,
            location: undefined,
          };
          // biome-ignore lint/style/noUselessElse: <explanation>
        } else {
          const result = await getAndMappingLocation(options);
          if (result) {
            return {
              permissionStatus: permissionStatus.status,
              ...result,
            };
          }
        }

        // Permission not granted yet, request it
        const granted = await requestUserLocationPermission();
        if (!granted) {
          return {
            permissionStatus: PermissionStatus.PERMISSION_NOT_GRANTED,
            shouldOpenSetting: true,
          };
        }
      }

      // Permission granted, get location
      const res = await getAndMappingLocation(options);
      if (res) {
        return {
          permissionStatus: permissionStatus.status,
          ...res,
        };
      }
    } catch (error) {
      console.error("getCurrentLocation", error);
      return null;
    }

    // Fallback to IP-based location
    const { location } = await getUserLocation({});
    return {
      shouldOpenSetting: false,
      permissionStatus: PermissionStatus.PERMISSION_NOT_GRANTED,
      ...location,
    };
  };

  const openLocationSettings = async () => {
    try {
      await openSystemSettings();
      return true;
    } catch (error) {
      console.error("openLocationSettings", error);
      return false;
    }
  };

  return {
    getCurrentLocation,
    openLocationSettings,
  };
};

const isUserShareLocationPermission = (permissionStatus: PermissionStatus) => {
  return [
    PermissionStatus.PERMISSION_ALWAYS,
    PermissionStatus.PERMISSION_WHEN_USE,
  ].includes(permissionStatus);
};

export const checkUserLocationPermission = async () => {
  try {
    const response = await checkLocationPermission();
    const data = response?.data;
    if (data?.permissionStatus) {
      const status = data.permissionStatus as PermissionStatus;
      const isGranted = isUserShareLocationPermission(status);
      const isDenied = status === PermissionStatus.PERMISSION_DENIED;

      return {
        isGranted,
        isDenied,
        status,
      };
    }
    return {
      isGranted: false,
      isDenied: false,
      status: PermissionStatus.PERMISSION_NOT_GRANTED,
    };
  } catch (error) {
    console.log("Is User Share Location Permission Request", error);
    return {
      isGranted: false,
      isDenied: false,
      status: PermissionStatus.PERMISSION_NOT_GRANTED,
    };
  }
};

export const requestUserLocationPermission = async () => {
  try {
    const response = await requestLocationPermission();
    const data = response?.data;
    if (data) {
      return isUserShareLocationPermission(
        data?.permissionStatus as PermissionStatus
      );
    }
    return false;
  } catch (error) {
    console.log("Is User Share Location Permission Request", error);
    return false;
  }
};

const getAndMappingLocation = async (options?: GetLocationOptions) => {
  try {
    const res = await getLocation(options);
    if (res?.data?.latitude) {
      const { location } = await getUserLocation({
        latitude: res.data.latitude,
        longitude: res.data.longitude,
      });
      return {
        location: location,
        shouldOpenSetting: !res.errorCode,
      };
    }
  } catch (error) {
    console.error("getCurrentLocation", error);
    return null;
  }
};
