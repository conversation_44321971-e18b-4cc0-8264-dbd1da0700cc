import { defineConfig } from 'vitest/config'
import tsconfigPaths from 'vite-tsconfig-paths'
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import path from 'path'

export default defineConfig({
  plugins: [tsconfigPaths()],
  test: {
    environment: 'happy-dom',
    globals: true,
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    coverage: {
      provider: 'istanbul', // or 'v8',
      reporter: ['text', 'json', 'html'],

    },
  }
})