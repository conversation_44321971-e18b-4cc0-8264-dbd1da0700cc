import { checkZlpSdkExisted, type JSSDKResponse } from "..";
import type {
  GetLocationOptions,
  LocationData,
  LocationErrorCode,
} from "./type";

export const getLocation = async (options?: GetLocationOptions) => {
  if (!checkZlpSdkExisted()) {
    throw {
      status: "failed",
      errorCode: "ZLP SDK not existed",
    };
  }
  return window.zlpSdk.Location?.getLocation(options)
    .then((response: JSSDKResponse<LocationData, LocationErrorCode>) => {
      if (!response.data) {
        throw response;
      }
      return response;
    })
    .catch((error: Error) => {
      if (error instanceof Error) {
        return Promise.reject(error);
      }
    });
};
