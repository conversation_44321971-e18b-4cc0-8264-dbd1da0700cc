// /* eslint-disable no-undef */
import isZ<PERSON> from '@/lib/isZPA';
import { getAppInfo, getDeviceIdZPA } from '@/lib/zlpSdk/device';

export const defaultHeaderInterceptor = async (request: RequestInit) => {
  const [deviceId, appInfo] = await Promise.all([getDeviceIdZPA(), getAppInfo()]);

  return {
    ...request,
    headers: {
      ...request.headers,
      // 'x-mini-app-version': zpiPackageJson?.version,
      'x-platform': isZPA ? 'ZPA' : 'ZMP',
      'x-device-id': deviceId,
      'x-user-agent': appInfo.userAgent,
      // 'x-device-os': appInfo.os,
      // 'x-app-version': appInfo.appVersion,
    },
  };
};
