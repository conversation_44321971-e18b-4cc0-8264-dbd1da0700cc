import { APP_ELEMENT_ID } from "@/constants";
import React, { useEffect } from "react";

interface FontSizeProviderProps {
  fontSize?: number;
  children?: React.ReactNode;
}

const FontSizeProvider: React.FC<FontSizeProviderProps> = ({ 
  fontSize = 16, 
  children 
}) => {
  useEffect(() => {
    // Create style element
    const styleElement = document.createElement('style');
    styleElement.id = APP_ELEMENT_ID
    styleElement.innerHTML = `
      :root {
        --font-size: ${fontSize}px;
        font-size: var(--font-size, 16px)!important;
      }
    `;
    
    // Add to document head
    document.head.appendChild(styleElement);
    
    // Cleanup function to remove style on unmount
    return () => {
      const element = document.getElementById(APP_ELEMENT_ID);
      if (element) {
        element.remove();
      }
    };
  }, [fontSize]);

  return <>{children}</>;
};

export default FontSizeProvider;