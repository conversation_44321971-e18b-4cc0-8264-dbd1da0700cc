const QC_HOSTS = ["socialdev.zalopay.vn", "localhost"];
const STG_HOSTS = ["socialstg.zalopay.vn"];

export const withBaseLocationUrl = (): string => {
    let url = "https://zlp-movie-api.zalopay.vn/v2/movie/user/location";
    const hostName = window.location.hostname;
    if (QC_HOSTS.includes(hostName)) {
        url = "https://zlpdev-movie-api.zalopay.vn/v2/movie/user/location";
    } else if (STG_HOSTS.includes(hostName)) {
        url = "https://zlpstg-movie-api.zalopay.vn/v2/movie/user/location";
    }
    return url;
};