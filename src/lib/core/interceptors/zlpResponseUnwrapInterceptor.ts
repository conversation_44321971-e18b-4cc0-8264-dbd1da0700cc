/* eslint-disable no-undef */

import { ServiceResponseSubcode } from '../types';
import RequestBuilder, { SuccessfulResponse } from '../requestBuilder';

export const zlpResponseUnwrapInterceptor = async ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => {
  if (response.ok) {
    try {
      const rawResp = (await response.clone().json()) as SuccessfulResponse<ResponseType>;
      if (rawResp.code === ServiceResponseSubcode.SUCCESS) {
        const jsonHandler = async () => {
          return rawResp.data;
        };
        response.json = jsonHandler;
        response.clone = () => {
          return {
            ...response,
            json: json<PERSON>andler,
          };
        };
      }
    } catch (err) {}
  }
  return { response, RequestBuilder };
};
