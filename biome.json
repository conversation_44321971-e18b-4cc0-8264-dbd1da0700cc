{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["./dist", "./node_modules"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "off"}, "style": {"useImportType": "off", "useSelfClosingElements": "off", "noUnusedTemplateLiteral": "off"}, "a11y": {"noRedundantRoles": "off", "useSemanticElements": "off", "useFocusableInteractive": "off"}, "suspicious": {"noArrayIndexKey": "off", "noExplicitAny": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "css": {"formatter": {"enabled": false}, "linter": {"enabled": false}}}