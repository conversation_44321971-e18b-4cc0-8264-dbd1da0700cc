import { formatCurrency } from "../../utils/formatCurrency";
import { OfferPromotionDetail, PromotionType, ValueOf } from "./types";

const getPromotionStyles = (type: ValueOf<typeof PromotionType>) => {
    let style = {};
    switch (type) {
        case PromotionType.PROMOTION_TYPE_DISCOUNT:
            style = {
                "--promotion-badge-text-background-color": "var(--color-secondary-red)",
            };
            break;
        case PromotionType.PROMOTION_TYPE_CASHBACK:
            style = {

                "--promotion-badge-text-background-color": "var(--color-primary-green)",
            };
            break;
        case PromotionType.PROMOTION_TYPE_LOYALTY_COIN:
            style = {

                "--promotion-badge-text-background-color": "var(--color-secondary-orange)",
            };
            break;
        default:
            style = {
                "--promotion-badge-text-background-color": "var(--color-secondary-red)",
            };
            break;
    }
    return style;
};

const getPromotionText = (promotionDetail: OfferPromotionDetail) => {
    if (!promotionDetail.optimal_promo || promotionDetail.optimal_promo.amount == null || promotionDetail.optimal_promo.type == null) {
        return "";
    }
    const amount = formatCurrency(Number(promotionDetail.optimal_promo.amount));
    const type = promotionDetail.optimal_promo.type;
    switch (type) {
        case PromotionType.PROMOTION_TYPE_DISCOUNT:
            return `Giảm ${amount}`;
        case PromotionType.PROMOTION_TYPE_CASHBACK:
            return `Hoàn ${amount}`;
        case PromotionType.PROMOTION_TYPE_LOYALTY_COIN:
            return `+${promotionDetail.optimal_promo.amount} xu`;
        default:
            return `Giảm ${amount}`;
    }
};

export { getPromotionStyles, getPromotionText };
