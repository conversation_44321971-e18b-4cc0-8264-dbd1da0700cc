
export async function getDeviceIdZPA(): Promise<string> {
  try {
    const response = await window.zlpSdk?.Device?.getDeviceId();
    return response.data?.deviceId;
  } catch (error) {
    console.log('getDeviceIdZPA error', error);
    return '';
  }
}

export type AppInfo = {
  environment: string;
  appVersion: string;
  platform: string;
  os: string;
  userAgent: string;
};

export async function getAppInfo(): Promise<AppInfo> {
  return window.zlpSdk.Device?.appInfo()
    .then((response: { data: AppInfo }) => {
      return response.data;
    })
    .catch((error: { errorCode: any; }) => {
      console.error(error.errorCode);
      return {} as AppInfo;
    });
}

export * from './openSystemSettings';
