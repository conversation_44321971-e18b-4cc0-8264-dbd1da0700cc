/* eslint-disable no-undef */

import { getZ<PERSON>Token } from "../../getZLPToken";

export const zlpAuthorizeInterceptor = async (request: RequestInit) => {
  let authenHeaders: any = {};

  const zlp_token = (await getZLPToken()) || '';
  if (zlp_token) {
    authenHeaders = {
      ...authenHeaders,
      Authorization: `Bearer ${zlp_token}`,
    };
  }

  return {
    ...request,
    headers: {
      ...request.headers,
      ...authenHeaders,
    },
  };
};
