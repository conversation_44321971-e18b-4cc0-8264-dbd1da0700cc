import { type JSSDKResponse, checkZlpSdkExisted } from "..";
import type { RequestLocationPermissionData } from "./type";

export const requestLocationPermission = async () => {
    if (!checkZlpSdkExisted()) {
            throw {
              status: "failed",
              errorCode: "ZLP SDK not existed",
            };
          }
    return window?.zlpSdk?.Location?.requestLocationPermission()
        .then((response: JSSDKResponse<RequestLocationPermissionData, any>) => {
            return response;
        })
        .catch((error: Error) => {
            if(error instanceof Error) {
                return Promise.reject(error);
            }
        });
}
