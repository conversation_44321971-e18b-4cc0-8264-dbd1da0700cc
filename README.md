# Rsbuild project

## Setup

Install the dependencies:

```bash
pnpm install
```

## Get started

Start the dev server:

```bash
pnpm dev
```

Build the app for production:

```bash
pnpm run build
```

Preview the production build locally:

```bash
pnpm run preview
```
sdk/
├── README.md
├── scripts/
│   ├── build-deploy-zpi.sh
│   └── load-env-with-arg.sh
├── package.json
└── pnpm.lockb
└── src
    ├── component/
    │   └── ui
    ├── features/
    ├── hooks/
    ├── store/
    ├── styles
    ├── utils
    ├── App.tsx
    ├── index.tsx