import { describe, it, expect, vi } from 'vitest';
import { getPromotionStyles, getPromotionText } from '../utils';
import { PromotionType } from '../types';

// Mock formatCurrency
vi.mock('../../../utils/formatCurrency', () => ({
  formatCurrency: (n: number) => `formatted-${n}`,
}));

describe('getPromotionStyles', () => {
  it('returns correct style for DISCOUNT', () => {
    expect(getPromotionStyles(PromotionType.PROMOTION_TYPE_DISCOUNT)).toEqual({
      '--promotion-badge-text-background-color': 'var(--color-secondary-red)',
    });
  });
  it('returns correct style for CASHBACK', () => {
    expect(getPromotionStyles(PromotionType.PROMOTION_TYPE_CASHBACK)).toEqual({
      '--promotion-badge-text-background-color': 'var(--color-primary-green)',
    });
  });
  it('returns correct style for LOYALTY_COIN', () => {
    expect(getPromotionStyles(PromotionType.PROMOTION_TYPE_LOYALTY_COIN)).toEqual({
      '--promotion-badge-text-background-color': 'var(--color-secondary-orange)',
    });
  });
  it('returns default style for unknown type', () => {
    expect(getPromotionStyles('UNKNOWN' as any)).toEqual({
      '--promotion-badge-text-background-color': 'var(--color-secondary-red)',
    });
  });
});

describe('getPromotionText', () => {
  it('returns correct text for DISCOUNT', () => {
    const detail = {
      optimal_promo: { amount: 1000, type: PromotionType.PROMOTION_TYPE_DISCOUNT },
    } as any;
    expect(getPromotionText(detail)).toBe('Giảm formatted-1000');
  });
  it('returns correct text for CASHBACK', () => {
    const detail = {
      optimal_promo: { amount: 2000, type: PromotionType.PROMOTION_TYPE_CASHBACK },
    } as any;
    expect(getPromotionText(detail)).toBe('Hoàn formatted-2000');
  });
  it('returns correct text for LOYALTY_COIN', () => {
    const detail = {
      optimal_promo: { amount: 3000, type: PromotionType.PROMOTION_TYPE_LOYALTY_COIN },
    } as any;
    expect(getPromotionText(detail)).toBe('+3000 xu');
  });
  it('returns default text for unknown type', () => {
    const detail = {
      optimal_promo: { amount: 4000, type: 'UNKNOWN' },
    } as any;
    expect(getPromotionText(detail)).toBe('Giảm formatted-4000');
  });
  it('returns empty string if amount is missing', () => {
    const detail = {
      optimal_promo: { type: PromotionType.PROMOTION_TYPE_DISCOUNT },
    } as any;
    expect(getPromotionText(detail)).toBe('');
  });
  it('returns empty string if type is missing', () => {
    const detail = {
      optimal_promo: { amount: 5000 },
    } as any;
    expect(getPromotionText(detail)).toBe('');
  });
  it('returns empty string if optimal_promo is missing', () => {
    const detail = {} as any;
    expect(getPromotionText(detail)).toBe('');
  });
});
