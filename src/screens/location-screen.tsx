import LocationPicker, {
  type CurrentLocation,
} from "@/features/location-picker";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const LocationScreen = () => {
  const navigate = useNavigate();
  const [location, setLocation] = useState<CurrentLocation | null>(null);
  return (
    <div>
      <Button
        onClick={() => {
          navigate("/");
        }}
      >
        Back to home
      </Button>
      <p>Click 👇🏻</p>
      <LocationPicker
        onLocationChange={(location) => location && setLocation(location)}
      />
      <LocationPicker
        title="Sử dụng vị trí của tôi"
        onLocationChange={(location) => location && setLocation(location)}
      />
      {location && (
        <pre className="p-4 mt-4 overflow-auto bg-gray-100 rounded">
          <code className="text-xs">{JSON.stringify(location, null, 2)}</code>
        </pre>
      )}
    </div>
  );
};

export default LocationScreen;
